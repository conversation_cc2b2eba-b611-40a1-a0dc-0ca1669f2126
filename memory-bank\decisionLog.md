# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-30 16:55:01 - Log of updates made.

*

## Decision

* [2025-07-30 17:23:54] - 腳本狀態恢復策略改進
  - 只在狀態確實為運行中且有有效話題ID時才恢復狀態
  - 增加頁面加載完整性檢查，避免在不完整頁面上執行操作
  - 使用更健壯的錯誤檢測機制，包括HTTP狀態碼和頁面內容檢查

## Rationale

* 原有的狀態恢復邏輯可能在頁面未完全加載或出現錯誤時仍嘗試恢復，導致腳本行為異常
* 需要更準確的頁面狀態檢測來避免在404或錯誤頁面上執行不必要的操作
* 時序控制問題可能導致腳本在頁面未穩定時就開始執行，需要更好的等待機制

## Implementation Details

* 修改 `loadState()` 函數，增加運行狀態和話題ID的有效性檢查
* 新增 `isPageLoadedProperly()` 函數檢查頁面基本元素是否存在
* 改進 `is404Page()` 函數，增加HTTP狀態碼檢查和更多錯誤文本匹配
* 優化 `handleTopicPage()` 函數的錯誤處理和重試邏輯
* 修復已淘汰的 `performance.navigation` API 使用問題