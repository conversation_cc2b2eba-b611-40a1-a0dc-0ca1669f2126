// ==UserScript==
// @name         linux.do一體化自動腳本
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  整合自動點讚、閱讀模擬、話題瀏覽的一體化腳本
// <AUTHOR> Script
// @license      AGPL-3.0-or-later
// @match        https://linux.do/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // === 全局配置和狀態管理 ===
    const DEBUG_MODE = true;
    
    // 默認配置
    const DEFAULT_CONFIG = {
        // 瀏覽設置
        browse: {
            topicStayTime: 7000,
            processingDirection: "+",
            processingStep: 1,
            autoStart: false
        },
        // 閱讀設置
        reading: {
            baseDelay: 2500,
            randomDelayRange: 800,
            minReqSize: 8,
            maxReqSize: 15, // 減少批次大小，避免請求過大
            minReadTime: 800,
            maxReadTime: 3000,
            enabled: true,
            ensureComplete: true, // 確保完整閱讀
            autoStart: true, // 自動啟動閱讀模擬（獨立於話題瀏覽）
            delayAfterPageLoad: 3000 // 頁面載入後延遲多久開始閱讀模擬
        },
        // 點讚設置
        liking: {
            minDelay: 200,
            maxDelay: 500,
            enabled: true,
            maxLikesPerTopic: 5,
            // 自動定時點讚功能
            autoLiking: {
                enabled: false, // 是否啟用每小時自動點讚
                hourlyTarget: 25, // 每小時點讚目標數量
                checkInterval: 60000, // 檢查間隔(毫秒) - 1分鐘
                minTopicDelay: 3000, // 話題間最小延遲
                maxTopicDelay: 8000, // 話題間最大延遲
                randomStart: true // 是否隨機化開始時間
            }
        },
        // 抽獎回覆設置
        lottery: {
            enabled: false, // 是否啟用抽獎自動回覆
            checkInterval: 300000, // 檢查間隔(毫秒) - 5分鐘
            replyDelay: {
                min: 3000, // 最小回覆延遲
                max: 8000  // 最大回覆延遲
            },
            onlyToday: true, // 只回覆今天的話題
            maxRepliesPerHour: 10, // 每小時最大回覆數量
            replies: [
                "参与一下",
                "来抽奖啦",
                "抽抽抽抽",
                "中奖中奖",
                "中中中中",
                "参与参与",
                "希望中奖",
                "重在参与",
                "支持支持",
                "分母+1",
                "来了来了",
                "谢谢佬友",
                "好运连连",
                "春风得意",
                "福星高照",
                "吉星高照",
                "鸿运齐天"
            ]
        },
        // 全局設置
        global: {
            enableAllFeatures: true,
            workflowMode: true // true: 順序執行所有功能，false: 僅瀏覽
        }
    };

    // 配置更新監聽器
    let configWatcher = {
        timer: null,
        lastCheck: Date.now(),
        checkInterval: 2000 // 2秒檢查一次
    };
    
    // 全局狀態
    let globalState = {
        isRunning: false,
        currentTopicId: null,
        processedCount: 0,
        currentPhase: 'idle', // idle, browsing, reading, liking
        config: { ...DEFAULT_CONFIG },
        // 點讚限制狀態
        liking: {
            hourlyCount: 0,
            lastResetTime: Date.now(),
            maxHourlyLikes: 25
        },
        // 自動點讚定時器狀態
        autoLiking: {
            isActive: false, // 是否正在執行自動點讚
            timer: null, // 主定時器
            checkTimer: null, // 檢查定時器
            currentHourStart: 0, // 當前小時開始時間
            todayLiked: 0, // 今日已點讚數量
            lastLikeTime: 0, // 上次點讚時間
            waitingForNextHour: false // 是否等待下一小時
        },
        // 抽獎回覆狀態
        lottery: {
            isActive: false, // 是否正在執行抽獎回覆
            timer: null, // 檢查定時器
            hourlyCount: 0, // 當前小時回覆數量
            lastResetTime: Date.now(), // 上次重置時間
            lastUsedReply: '', // 上次使用的回覆內容
            processedTopics: new Set(), // 已處理的話題ID
            todayProcessed: 0 // 今日已處理數量
        }
    };

    // 載入配置
    function loadConfig() {
        try {
            const savedConfig = GM_getValue('unified_config', null);
            if (savedConfig) {
                const parsedConfig = JSON.parse(savedConfig);
                // 深度合併配置，保護結構完整性
                const newConfig = {
                    ...DEFAULT_CONFIG,
                    browse: { ...DEFAULT_CONFIG.browse, ...parsedConfig.browse },
                    reading: { ...DEFAULT_CONFIG.reading, ...parsedConfig.reading },
                    liking: {
                        ...DEFAULT_CONFIG.liking,
                        ...parsedConfig.liking,
                        autoLiking: {
                            ...DEFAULT_CONFIG.liking.autoLiking,
                            ...(parsedConfig.liking?.autoLiking || {})
                        }
                    },
                    lottery: {
                        ...DEFAULT_CONFIG.lottery,
                        ...parsedConfig.lottery,
                        replies: parsedConfig.lottery?.replies || DEFAULT_CONFIG.lottery.replies
                    },
                    global: { ...DEFAULT_CONFIG.global, ...parsedConfig.global }
                };
                
                globalState.config = newConfig;
                debugLog('配置載入成功:', globalState.config);
                
                // 檢查配置更新時間戳
                checkConfigUpdateNotification();
            } else {
                debugLog('未找到已保存的配置，使用預設配置');
                globalState.config = { ...DEFAULT_CONFIG };
            }
        } catch (error) {
            debugLog('載入配置失敗:', error);
            // 出錯時使用預設配置
            globalState.config = { ...DEFAULT_CONFIG };
        }
    }
    
    // 檢查配置更新通知
    function checkConfigUpdateNotification() {
        try {
            const updateTimestamp = GM_getValue('config_update_timestamp', '0');
            const lastChecked = GM_getValue('last_config_check', '0');
            
            if (updateTimestamp > lastChecked) {
                debugLog('檢測到配置更新，重新載入配置');
                // 重新載入配置（避免遞歸）
                const savedConfig = GM_getValue('unified_config', null);
                if (savedConfig) {
                    const parsedConfig = JSON.parse(savedConfig);
                    globalState.config = {
                        ...DEFAULT_CONFIG,
                        browse: { ...DEFAULT_CONFIG.browse, ...parsedConfig.browse },
                        reading: { ...DEFAULT_CONFIG.reading, ...parsedConfig.reading },
                        liking: {
                            ...DEFAULT_CONFIG.liking,
                            ...parsedConfig.liking,
                            autoLiking: {
                                ...DEFAULT_CONFIG.liking.autoLiking,
                                ...(parsedConfig.liking?.autoLiking || {})
                            }
                        },
                        global: { ...DEFAULT_CONFIG.global, ...parsedConfig.global }
                    };
                    debugLog('配置已根據更新通知重新載入');
                }
                GM_setValue('last_config_check', Date.now().toString());
            }
        } catch (error) {
            debugLog('檢查配置更新通知失敗:', error);
        }
    }
    
    // 啟動配置更新監聽器
    function startConfigWatcher() {
        if (configWatcher.timer) {
            clearInterval(configWatcher.timer);
        }
        
        configWatcher.timer = setInterval(() => {
            try {
                const updateTimestamp = GM_getValue('config_update_timestamp', '0');
                
                if (parseInt(updateTimestamp) > configWatcher.lastCheck) {
                    debugLog('檢測到配置更新，重新載入並應用');
                    
                    // 保存舊配置以便比較
                    const oldConfig = JSON.parse(JSON.stringify(globalState.config));
                    
                    // 重新載入配置
                    loadConfig();
                    
                    // 應用配置更新
                    applyConfigChanges(oldConfig, globalState.config);
                    
                    // 更新檢查時間
                    configWatcher.lastCheck = parseInt(updateTimestamp);
                    
                    // 更新控制面板
                    updateControlPanel();
                    
                    updateStatus('配置已即時更新');
                }
            } catch (error) {
                debugLog('配置監聽器出錯:', error);
            }
        }, configWatcher.checkInterval);
        
        debugLog(`配置更新監聽器已啟動，檢查間隔: ${configWatcher.checkInterval}ms`);
    }
    
    // 停止配置更新監聽器
    function stopConfigWatcher() {
        if (configWatcher.timer) {
            clearInterval(configWatcher.timer);
            configWatcher.timer = null;
            debugLog('配置更新監聽器已停止');
        }
    }
    
    // 應用配置更新
    function applyConfigChanges(oldConfig, newConfig) {
        try {
            debugLog('應用配置更新:', { old: oldConfig, new: newConfig });
            
            // 檢查自動閱讀設定更新
            if (oldConfig.reading.autoStart !== newConfig.reading.autoStart) {
                if (newConfig.reading.autoStart && isTopicPage() && !globalState.isRunning) {
                    debugLog('啟動自動閱讀模擬');
                    startAutoReading();
                } else if (!newConfig.reading.autoStart) {
                    debugLog('停止自動閱讀模擬');
                    stopAutoReading();
                }
            }
            
            // 檢查定時點讚設定更新
            if (oldConfig.liking.autoLiking.enabled !== newConfig.liking.autoLiking.enabled) {
                if (newConfig.liking.autoLiking.enabled) {
                    debugLog('啟動定時點讚系統');
                    startAutoLikingSystem();
                } else {
                    debugLog('停止定時點讚系統');
                    stopAutoLikingSystem();
                }
            } else if (newConfig.liking.autoLiking.enabled) {
                // 如果定時點讚已啟用，但其他設定更新了，重新啟動
                const autoLikingChanged = JSON.stringify(oldConfig.liking.autoLiking) !== JSON.stringify(newConfig.liking.autoLiking);
                if (autoLikingChanged) {
                    debugLog('定時點讚設定已更新，重新啟動系統');
                    stopAutoLikingSystem();
                    setTimeout(() => startAutoLikingSystem(), 1000);
                }
            }

            // 檢查抽獎系統設定更新
            if (oldConfig.lottery.enabled !== newConfig.lottery.enabled) {
                if (newConfig.lottery.enabled) {
                    debugLog('啟動抽獎自動回覆系統');
                    startLotterySystem();
                } else {
                    debugLog('停止抽獎自動回覆系統');
                    stopLotterySystem();
                }
            } else if (newConfig.lottery.enabled) {
                // 如果抽獎系統已啟用，但其他設定更新了，重新啟動
                const lotteryChanged = JSON.stringify(oldConfig.lottery) !== JSON.stringify(newConfig.lottery);
                if (lotteryChanged) {
                    debugLog('抽獎系統設定已更新，重新啟動系統');
                    stopLotterySystem();
                    setTimeout(() => startLotterySystem(), 1000);
                }
            }

            debugLog('配置更新已應用');

        } catch (error) {
            debugLog('應用配置更新失敗:', error);
        }
    }

    // 保存配置
    function saveConfig() {
        try {
            const configString = JSON.stringify(globalState.config);
            
            // 強制同步保存，使用多次重試機制
            let saveSuccess = false;
            let retryCount = 0;
            const maxRetries = 3;
            
            while (!saveSuccess && retryCount < maxRetries) {
                try {
                    GM_setValue('unified_config', configString);
                    
                    // 立即驗證保存是否成功
                    const saved = GM_getValue('unified_config', null);
                    if (saved === configString) {
                        saveSuccess = true;
                        debugLog(`配置保存成功 (第${retryCount + 1}次嘗試)`);
                    } else {
                        retryCount++;
                        debugLog(`配置保存驗證失敗，準備重試 (${retryCount}/${maxRetries})`);
                        // 短暫延遲後重試
                        if (retryCount < maxRetries) {
                            // 同步延遲
                            const start = Date.now();
                            while (Date.now() - start < 100) {
                                // 忙等待100ms
                            }
                        }
                    }
                } catch (innerError) {
                    retryCount++;
                    debugLog(`配置保存出錯，重試 (${retryCount}/${maxRetries}):`, innerError);
                }
            }
            
            if (saveSuccess) {
                debugLog('配置已成功保存並驗證:', globalState.config);
                // 立即通知其他功能配置已更新
                broadcastConfigUpdate();
                return true;
            } else {
                debugLog('配置保存最終失敗，嘗試次數已用盡');
                return false;
            }
        } catch (error) {
            debugLog('保存配置失敗:', error);
            return false;
        }
    }
    
    // 廣播配置更新通知
    function broadcastConfigUpdate() {
        try {
            // 設置一個標記，通知其他實例配置已更新
            GM_setValue('config_update_timestamp', Date.now().toString());
            debugLog('配置更新通知已發送');
        } catch (error) {
            debugLog('發送配置更新通知失敗:', error);
        }
    }

    // 保存狀態
    function saveState() {
        try {
            const state = {
                isRunning: globalState.isRunning,
                currentTopicId: globalState.currentTopicId,
                processedCount: globalState.processedCount,
                currentPhase: globalState.currentPhase,
                timestamp: Date.now()
            };
            GM_setValue('unified_state', JSON.stringify(state));
            debugLog('狀態已保存:', state);
        } catch (error) {
            debugLog('保存狀態失敗:', error);
        }
    }

    // 載入狀態
    function loadState() {
        try {
            const savedState = GM_getValue('unified_state', null);
            if (savedState) {
                const state = JSON.parse(savedState);
                const timeDiff = Date.now() - state.timestamp;
                
                // 只有在60秒內的狀態才視為有效
                if (timeDiff < 60000 && state.isRunning) {
                    globalState.isRunning = state.isRunning;
                    globalState.currentTopicId = state.currentTopicId;
                    globalState.processedCount = state.processedCount;
                    globalState.currentPhase = state.currentPhase;
                    debugLog('狀態已恢復:', state);
                    return true;
                } else {
                    debugLog('狀態已過期或無效，清除舊狀態');
                    clearState();
                }
            }
            return false;
        } catch (error) {
            debugLog('載入狀態失敗:', error);
            return false;
        }
    }

    // 清除狀態
    function clearState() {
        try {
            GM_setValue('unified_state', null);
            debugLog('狀態已清除');
        } catch (error) {
            debugLog('清除狀態失敗:', error);
        }
    }

    // === 點讚限制管理 ===
    // 載入點讚限制狀態
    function loadLikingLimits() {
        try {
            const savedLimits = GM_getValue('liking_limits', null);
            if (savedLimits) {
                const limits = JSON.parse(savedLimits);
                const now = Date.now();
                const hoursPassed = (now - limits.lastResetTime) / (1000 * 60 * 60);
                
                // 如果超過1小時，重置計數
                if (hoursPassed >= 1) {
                    globalState.liking.hourlyCount = 0;
                    globalState.liking.lastResetTime = now;
                    debugLog('點讚計數已重置（超過1小時）');
                } else {
                    globalState.liking.hourlyCount = limits.hourlyCount || 0;
                    globalState.liking.lastResetTime = limits.lastResetTime || now;
                    debugLog(`載入點讚限制：已點讚 ${globalState.liking.hourlyCount}/${globalState.liking.maxHourlyLikes}`);
                }
            }
        } catch (error) {
            debugLog('載入點讚限制失敗:', error);
        }
    }

    // 保存點讚限制狀態
    function saveLikingLimits() {
        try {
            const limits = {
                hourlyCount: globalState.liking.hourlyCount,
                lastResetTime: globalState.liking.lastResetTime
            };
            GM_setValue('liking_limits', JSON.stringify(limits));
            debugLog(`保存點讚限制：${globalState.liking.hourlyCount}/${globalState.liking.maxHourlyLikes}`);
        } catch (error) {
            debugLog('保存點讚限制失敗:', error);
        }
    }

    // 檢查是否可以點讚
    function canLikeMore() {
        const now = Date.now();
        const hoursPassed = (now - globalState.liking.lastResetTime) / (1000 * 60 * 60);
        
        // 如果超過1小時，重置計數
        if (hoursPassed >= 1) {
            globalState.liking.hourlyCount = 0;
            globalState.liking.lastResetTime = now;
            saveLikingLimits();
            debugLog('點讚計數已自動重置');
        }
        
        return globalState.liking.hourlyCount < globalState.liking.maxHourlyLikes;
    }

    // 記錄點讚
    function recordLike(count = 1) {
        globalState.liking.hourlyCount += count;
        saveLikingLimits();
        debugLog(`記錄點讚 +${count}，當前：${globalState.liking.hourlyCount}/${globalState.liking.maxHourlyLikes}`);
    }

    // 獲取點讚狀態信息
    function getLikingStatus() {
        const now = Date.now();
        const timeSinceReset = now - globalState.liking.lastResetTime;
        const remainingTime = (60 * 60 * 1000) - timeSinceReset; // 距離重置還有多長時間
        const remainingLikes = globalState.liking.maxHourlyLikes - globalState.liking.hourlyCount;
        
        return {
            current: globalState.liking.hourlyCount,
            max: globalState.liking.maxHourlyLikes,
            remaining: remainingLikes,
            resetIn: Math.max(0, remainingTime),
            canLike: remainingLikes > 0 && remainingTime > 0
        };
    }

    // === 配置測試功能 ===
    function testConfigSaveLoad() {
        debugLog('開始配置測試...');
        
        // 備份當前配置
        const originalConfig = JSON.parse(JSON.stringify(globalState.config));
        
        // 修改一個測試值
        const testValue = Date.now();
        globalState.config.browse.topicStayTime = testValue;
        
        // 保存配置
        const saveSuccess = saveConfig();
        if (!saveSuccess) {
            debugLog('配置保存測試失敗');
            return false;
        }
        
        // 重新載入配置
        loadConfig();
        
        // 檢查是否正確載入
        const loadSuccess = globalState.config.browse.topicStayTime === testValue;
        
        // 還原原始配置
        globalState.config = originalConfig;
        saveConfig();
        
        debugLog('配置測試結果:', loadSuccess ? '成功' : '失敗');
        return loadSuccess;
    }
    
    // === 調試和日誌系統 ===
    let logBuffer = [];
    const MAX_LOG_BUFFER = 100;

    function debugLog(...args) {
        if (DEBUG_MODE) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[一體化腳本 ${timestamp}] ${args.join(' ')}`;
            
            logBuffer.push({
                timestamp: Date.now(),
                message: logMessage,
                args: args
            });
            
            if (logBuffer.length > MAX_LOG_BUFFER) {
                logBuffer = logBuffer.slice(-MAX_LOG_BUFFER);
            }
            
            console.log(logMessage);
        }
    }

    // === 工具函數 ===
    function safeQuerySelector(selector, parent = document) {
        try {
            if (!parent || typeof parent.querySelector !== 'function') {
                return null;
            }
            return parent.querySelector(selector);
        } catch (error) {
            debugLog('選擇器錯誤:', selector, error);
            return null;
        }
    }

    function isTopicPage() {
        return window.location.pathname.match(/^\/t\/topic\/\d+/) || 
               /^https:\/\/linux\.do\/t\/[^/]+\/\d+/.test(window.location.href);
    }

    function getCurrentTopicIdFromUrl() {
        const match1 = window.location.pathname.match(/^\/t\/topic\/(\d+)/);
        if (match1) return parseInt(match1[1]);
        
        const match2 = window.location.pathname.match(/^\/t\/[^/]+\/(\d+)/);
        if (match2) return parseInt(match2[1]);
        
        return null;
    }

    function getRandomDelay(min, max) {
        return Math.random() * (max - min) + min;
    }

    async function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // === 狀態顯示系統 ===
    let statusElement = null;

    function createStatusDisplay() {
        if (statusElement) return;

        statusElement = document.createElement('div');
        statusElement.id = 'unified-script-status';
        statusElement.style.cssText = `
            position: fixed;
            bottom: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 10000;
            max-width: 350px;
            word-wrap: break-word;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            font-family: Arial, sans-serif;
        `;

        if (document.body) {
            document.body.appendChild(statusElement);
        }
    }

    function updateStatus(message) {
        if (!statusElement) createStatusDisplay();
        if (statusElement) {
            statusElement.textContent = message;
            debugLog('狀態更新:', message);
        }
    }

    // === 控制面板系統 ===
    let panelState = {
        isMinimized: GM_getValue('panel_minimized', false),
        position: JSON.parse(GM_getValue('panel_position', '{"top": 70, "right": 20}'))
    };
    
    // 創建可拖曳的球形按鈕系統
    function createFloatingButton() {
        // 主小球容器
        const container = document.createElement('div');
        container.id = 'unified-floating-container';
        container.style.cssText = `
            position: fixed;
            z-index: 10001;
            pointer-events: none;
        `;
        
        // 主小球
        const button = document.createElement('div');
        button.id = 'unified-floating-button';
        button.innerHTML = '🚀';
        button.style.cssText = `
            position: relative;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: move;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            transition: none;
            user-select: none;
            will-change: transform, box-shadow;
            touch-action: none;
            pointer-events: auto;
        `;
        
        // 懸浮提示標籤
        const hoverLabel = document.createElement('div');
        hoverLabel.id = 'floating-hover-label';
        hoverLabel.textContent = 'Linux.do 腦本';
        hoverLabel.style.cssText = `
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            z-index: -1;
        `;
        
        // 組裝元素
        container.appendChild(button);
        container.appendChild(hoverLabel);
        
        // 設定初始位置
        updateContainerPosition();
        
        // 更新容器位置函數
        function updateContainerPosition() {
            container.style.top = panelState.position.top + 'px';
            container.style.right = panelState.position.right + 'px';
            
            // 更新懸浮標籤位置（相反方向顯示）
            const buttonLeftPosition = window.innerWidth - panelState.position.right;
            const isOnLeftSide = buttonLeftPosition < window.innerWidth / 2;
            
            if (isOnLeftSide) {
                // 小球在左邊，標籤顯示在右邊
                hoverLabel.style.left = '60px';
                hoverLabel.style.right = 'auto';
                debugLog('標籤定位：小球在左，標籤在右');
            } else {
                // 小球在右邊，標籤顯示在左邊
                hoverLabel.style.right = '60px';
                hoverLabel.style.left = 'auto';
                debugLog('標籤定位：小球在右，標籤在左');
            }
        }
        
        // 120fps 高性能拖曳系統
        let dragState = {
            isDragging: false,
            hasMoved: false,
            startX: 0,
            startY: 0,
            startTop: 0,
            startRight: 0,
            currentX: 0,
            currentY: 0,
            animationId: null,
            lastTime: 0,
            isAttached: false
        };
        
        // 120fps 動畫系統
        function animate() {
            if (!dragState.isDragging) return;
            
            const now = performance.now();
            if (now - dragState.lastTime > 8.33) { // 120fps = 8.33ms per frame
                const deltaX = dragState.startX - dragState.currentX;
                const deltaY = dragState.currentY - dragState.startY;
                
                let newTop = dragState.startTop + deltaY;
                let newRight = dragState.startRight + deltaX;
                
                // 邊界檢查
                newTop = Math.max(0, Math.min(newTop, window.innerHeight - 50));
                newRight = Math.max(0, Math.min(newRight, window.innerWidth - 50));
                
                // 使用 transform 提高性能
                container.style.transform = `translate(${-newRight + panelState.position.right}px, ${newTop - panelState.position.top}px)`;
                
                dragState.lastTime = now;
            }
            
            dragState.animationId = requestAnimationFrame(animate);
        }
        
        // 統一事件處理
        function getEventPos(e) {
            if (e.type.startsWith('touch')) {
                return {
                    x: e.touches[0]?.clientX || e.changedTouches[0]?.clientX,
                    y: e.touches[0]?.clientY || e.changedTouches[0]?.clientY
                };
            }
            return { x: e.clientX, y: e.clientY };
        }
        
        // 開始拖曳
        function startDrag(e) {
            const pos = getEventPos(e);
            dragState.isDragging = true;
            dragState.hasMoved = false;
            dragState.startX = pos.x;
            dragState.startY = pos.y;
            dragState.currentX = pos.x;
            dragState.currentY = pos.y;
            dragState.startTop = panelState.position.top;
            dragState.startRight = panelState.position.right;
            dragState.lastTime = performance.now();
            
            // 視覺效果
            button.style.cursor = 'grabbing';
            button.style.transform = 'scale(1.05)';
            button.style.boxShadow = '0 8px 30px rgba(0,0,0,0.5)';
            button.style.transition = 'none';
            hoverLabel.style.opacity = '0';
            
            // 開始 120fps 動畫
            dragState.animationId = requestAnimationFrame(animate);
            
            e.preventDefault();
            e.stopPropagation();
            debugLog('開始 120fps 拖曳');
        }
        
        // 拖曳過程
        function onDrag(e) {
            if (!dragState.isDragging) return;
            
            const pos = getEventPos(e);
            dragState.currentX = pos.x;
            dragState.currentY = pos.y;
            
            // 檢查是否真的移動了
            const deltaX = Math.abs(dragState.currentX - dragState.startX);
            const deltaY = Math.abs(dragState.currentY - dragState.startY);
            if (deltaX > 3 || deltaY > 3) {
                dragState.hasMoved = true;
            }
            
            e.preventDefault();
        }
        
        // 結束拖曳
        function endDrag(e) {
            if (!dragState.isDragging) return;
            
            dragState.isDragging = false;
            
            // 停止動畫循環
            if (dragState.animationId) {
                cancelAnimationFrame(dragState.animationId);
                dragState.animationId = null;
            }
            
            // 計算最終位置
            const deltaX = dragState.startX - dragState.currentX;
            const deltaY = dragState.currentY - dragState.startY;
            let finalTop = dragState.startTop + deltaY;
            let finalRight = dragState.startRight + deltaX;
            
            // 邊界限制
            finalTop = Math.max(0, Math.min(finalTop, window.innerHeight - 50));
            finalRight = Math.max(0, Math.min(finalRight, window.innerWidth - 50));
            
            // 智能雙向吸附到左右邊緣
            const currentLeft = window.innerWidth - finalRight;
            const snapThreshold = 50; // 吸附閾值
            
            if (currentLeft <= window.innerWidth / 2) {
                // 按鈕在左半部分，吸附到左邊緣
                finalRight = window.innerWidth - snapThreshold;
                debugLog('吸附到左邊緣，finalRight:', finalRight);
            } else {
                // 按鈕在右半部分，吸附到右邊緣
                finalRight = snapThreshold;
                debugLog('吸附到右邊緣，finalRight:', finalRight);
            }
            
            // 保存新位置
            panelState.position = { top: finalTop, right: finalRight };
            GM_setValue('panel_position', JSON.stringify(panelState.position));
            
            // 平滑吸附動畫
            container.style.transition = 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            container.style.transform = 'translate(0, 0)';
            updateContainerPosition();
            
            // 恢復視覺狀態
            setTimeout(() => {
                button.style.cursor = 'move';
                button.style.transform = 'scale(1)';
                button.style.boxShadow = '0 4px 15px rgba(0,0,0,0.3)';
                button.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';
                container.style.transition = 'none';
            }, 400);
            
            debugLog('拖曳結束，吸附到位置:', panelState.position);
            e.preventDefault();
        }
        
        // 點擊事件
        function onClick(e) {
            if (!dragState.hasMoved) {
                expandPanelSmart();
            }
            e.preventDefault();
            e.stopPropagation();
        }
        
        // 懸浮效果
        function onMouseEnter() {
            if (!dragState.isDragging) {
                button.style.transform = 'scale(1.1)';
                button.style.boxShadow = '0 6px 25px rgba(0,0,0,0.4)';
                hoverLabel.style.opacity = '1';
            }
        }
        
        function onMouseLeave() {
            if (!dragState.isDragging) {
                button.style.transform = 'scale(1)';
                button.style.boxShadow = '0 4px 15px rgba(0,0,0,0.3)';
                hoverLabel.style.opacity = '0';
            }
        }
        
        // 綁定事件
        button.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', onDrag, { passive: false });
        document.addEventListener('mouseup', endDrag);
        button.addEventListener('click', onClick);
        
        // 觸摸事件
        button.addEventListener('touchstart', startDrag, { passive: false });
        document.addEventListener('touchmove', onDrag, { passive: false });
        document.addEventListener('touchend', endDrag, { passive: false });
        
        // hover 效果
        button.addEventListener('mouseenter', onMouseEnter);
        button.addEventListener('mouseleave', onMouseLeave);
        
        document.body.appendChild(container);
        return container;
    }
    
    function createControlPanel() {
        // 檢查是否已存在
        if (safeQuerySelector('#unified-control-panel')) {
            return;
        }
        
        // 如果是最小化狀態，只創建球形按鈕
        if (panelState.isMinimized) {
            createFloatingButton();
            return;
        }

        const panel = document.createElement('div');
        panel.id = 'unified-control-panel';
        panel.style.cssText = `
            position: fixed;
            top: ${panelState.position.top}px;
            right: ${panelState.position.right}px;
            z-index: 10000;
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: Arial, sans-serif;
            min-width: 250px;
            transition: all 0.3s ease;
        `;

        panel.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <div style="font-weight: bold; color: #333;">
                    🚀 Linux.do 一體化腳本
                </div>
                <button id="minimize-panel" style="background: none; border: none; font-size: 16px; cursor: pointer; color: #666; padding: 0; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;" title="縮小">−</button>
            </div>
            <div style="margin-bottom: 10px;">
                <label style="display: flex; align-items: center; margin-bottom: 5px;">
                    <input type="checkbox" id="enable-all" ${globalState.config.global.enableAllFeatures ? 'checked' : ''}>
                    <span style="margin-left: 8px;">啟用所有功能</span>
                </label>
                <label style="display: flex; align-items: center; margin-bottom: 5px;">
                    <input type="checkbox" id="workflow-mode" ${globalState.config.global.workflowMode ? 'checked' : ''}>
                    <span style="margin-left: 8px;">工作流模式</span>
                </label>
            </div>
            <div style="margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                功能: 瀏覽→閱讀→點讚→下一話題
            </div>
            <div style="display: flex; gap: 8px; margin-bottom: 10px;">
                <button id="start-unified" style="flex: 1; padding: 8px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">開始</button>
                <button id="stop-unified" style="flex: 1; padding: 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">停止</button>
            </div>
            <div style="display: flex; gap: 8px;">
                <button id="config-unified" style="flex: 1; padding: 6px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">配置</button>
                <button id="status-unified" style="flex: 1; padding: 6px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">狀態</button>
            </div>
            <div style="margin-top: 8px;">
                <button id="test-config" style="width: 100%; padding: 6px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">測試配置</button>
            </div>
            <div style="margin-top: 8px;">
                <button id="toggle-auto-reading" style="width: 100%; padding: 6px; background: ${globalState.config.reading.autoStart ? '#007bff' : '#6c757d'}; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">${globalState.config.reading.autoStart ? '停止自動閱讀' : '啟動自動閱讀'}</button>
            </div>
            <div style="margin-top: 8px;">
                <button id="toggle-auto-liking" style="width: 100%; padding: 6px; background: ${globalState.config.liking.autoLiking.enabled ? '#dc3545' : '#28a745'}; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">${globalState.config.liking.autoLiking.enabled ? '停止定時點讚' : '啟動定時點讚'}</button>
            </div>
            <div style="margin-top: 8px;">
                <button id="reset-likes" style="width: 100%; padding: 6px; background: #ffc107; color: #212529; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">重置點讚計數</button>
            </div>
            <div style="margin-top: 8px;">
                <button id="toggle-lottery" style="width: 100%; padding: 6px; background: ${globalState.config.lottery.enabled ? '#dc3545' : '#28a745'}; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">${globalState.config.lottery.enabled ? '停止抽獎回覆' : '啟動抽獎回覆'}</button>
            </div>
            <div id="current-status" style="margin-top: 10px; padding: 8px; background: #e9ecef; border-radius: 4px; font-size: 12px; text-align: center;">
                待機中
            </div>
        `;

        // 綁定事件
        panel.querySelector('#enable-all').addEventListener('change', (e) => {
            globalState.config.global.enableAllFeatures = e.target.checked;
            const saveSuccess = saveConfig();
            if (saveSuccess) {
                if (globalState.isRunning) {
                    updateStatus('設定已保存，將在下次頁面載入時生效');
                } else {
                    updateStatus('全功能設定已更新');
                }
                debugLog('全功能設定:', e.target.checked);
            } else {
                // 保存失敗，還原狀態
                e.target.checked = !e.target.checked;
                globalState.config.global.enableAllFeatures = e.target.checked;
                updateStatus('設定保存失敗');
            }
        });

        panel.querySelector('#workflow-mode').addEventListener('change', (e) => {
            globalState.config.global.workflowMode = e.target.checked;
            const saveSuccess = saveConfig();
            if (saveSuccess) {
                if (globalState.isRunning) {
                    updateStatus('設定已保存，將在下次頁面載入時生效');
                } else {
                    updateStatus('工作流模式已更新');
                }
                debugLog('工作流模式:', e.target.checked);
            } else {
                // 保存失敗，還原狀態
                e.target.checked = !e.target.checked;
                globalState.config.global.workflowMode = e.target.checked;
                updateStatus('設定保存失敗');
            }
        });

        panel.querySelector('#start-unified').addEventListener('click', startUnifiedScript);
        panel.querySelector('#stop-unified').addEventListener('click', stopUnifiedScript);
        panel.querySelector('#config-unified').addEventListener('click', showConfigDialog);
        panel.querySelector('#status-unified').addEventListener('click', showStatusReport);
        panel.querySelector('#test-config').addEventListener('click', () => {
            const testResult = testConfigSaveLoad();
            if (testResult) {
                updateStatus('配置測試成功');
                alert('配置保存和載入功能正常！');
            } else {
                updateStatus('配置測試失敗');
                alert('配置保存或載入出現問題，請檢查瀏覽器設定或腋本權限。');
            }
        });
        
        // 縮小按鈕事件
        panel.querySelector('#minimize-panel').addEventListener('click', () => {
            minimizePanel();
        });
        
        // 自動閱讀開關
        panel.querySelector('#toggle-auto-reading').addEventListener('click', () => {
            globalState.config.reading.autoStart = !globalState.config.reading.autoStart;
            const saveSuccess = saveConfig();
            
            if (saveSuccess) {
                const button = panel.querySelector('#toggle-auto-reading');
                button.style.background = globalState.config.reading.autoStart ? '#007bff' : '#6c757d';
                button.textContent = globalState.config.reading.autoStart ? '停止自動閱讀' : '啟動自動閱讀';
                
                if (globalState.config.reading.autoStart && isTopicPage() && !globalState.isRunning) {
                    // 立即啟動自動閱讀
                    startAutoReading();
                    updateStatus('自動閱讀已啟用');
                } else {
                    // 停止自動閱讀
                    stopAutoReading();
                    updateStatus('自動閱讀已停用');
                }
                
                debugLog('自動閱讀設定已更新:', globalState.config.reading.autoStart);
            } else {
                updateStatus('設定更新失敗');
            }
        });
        
        // 定時點讚開關
        panel.querySelector('#toggle-auto-liking').addEventListener('click', () => {
            globalState.config.liking.autoLiking.enabled = !globalState.config.liking.autoLiking.enabled;
            const saveSuccess = saveConfig();
            
            if (saveSuccess) {
                const button = panel.querySelector('#toggle-auto-liking');
                button.style.background = globalState.config.liking.autoLiking.enabled ? '#dc3545' : '#28a745';
                button.textContent = globalState.config.liking.autoLiking.enabled ? '停止定時點讚' : '啟動定時點讚';
                
                if (globalState.config.liking.autoLiking.enabled) {
                    // 啟動定時點讚系統
                    startAutoLikingSystem();
                    updateStatus('定時點讚系統已啟動');
                } else {
                    // 停止定時點讚系統
                    stopAutoLikingSystem();
                    updateStatus('定時點讚系統已停止');
                }
                
                updateControlPanel(); // 更新控制面板狀態
                debugLog('定時點讚設定已更新:', globalState.config.liking.autoLiking.enabled);
            } else {
                updateStatus('設定更新失敗');
            }
        });
        panel.querySelector('#reset-likes').addEventListener('click', () => {
            if (confirm('確定要重置點讚計數嗎？這將清零當前小時的點讚記錄。')) {
                globalState.liking.hourlyCount = 0;
                globalState.liking.lastResetTime = Date.now();
                saveLikingLimits();

                // 強制更新控制面板
                setTimeout(() => {
                    updateControlPanel();
                    updateStatus('點讚計數已重置');
                    debugLog('手動重置點讚計數，新狀態:', getLikingStatus());
                }, 100);
            }
        });

        // 抽獎系統開關
        panel.querySelector('#toggle-lottery').addEventListener('click', () => {
            globalState.config.lottery.enabled = !globalState.config.lottery.enabled;
            const saveSuccess = saveConfig();

            if (saveSuccess) {
                const button = panel.querySelector('#toggle-lottery');
                button.style.background = globalState.config.lottery.enabled ? '#dc3545' : '#28a745';
                button.textContent = globalState.config.lottery.enabled ? '停止抽獎回覆' : '啟動抽獎回覆';

                if (globalState.config.lottery.enabled) {
                    // 啟動抽獎系統
                    startLotterySystem();
                    updateStatus('抽獎自動回覆系統已啟動');
                } else {
                    // 停止抽獎系統
                    stopLotterySystem();
                    updateStatus('抽獎自動回覆系統已停止');
                }

                updateControlPanel(); // 更新控制面板狀態
                debugLog('抽獎系統設定已更新:', globalState.config.lottery.enabled);
            } else {
                updateStatus('設定更新失敗');
            }
        });

        document.body.appendChild(panel);
    }
    
    // 縮小面板
    function minimizePanel() {
        const panel = safeQuerySelector('#unified-control-panel');
        if (panel) {
            // 保存當前位置
            panelState.position = {
                top: parseInt(panel.style.top),
                right: parseInt(panel.style.right)
            };
            
            // 移除面板
            panel.remove();
            
            // 創建球形按鈕
            createFloatingButton();
            
            // 保存縮小狀態
            panelState.isMinimized = true;
            GM_setValue('panel_minimized', true);
            GM_setValue('panel_position', JSON.stringify(panelState.position));
            
            debugLog('控制面板已縮小');
            updateStatus('控制面板已縮小，點擊球形按鈕可展開');
        }
    }
    
    // 展開面板
    function expandPanel() {
        const floatingButton = safeQuerySelector('#unified-floating-button');
        if (floatingButton) {
            floatingButton.remove();
        }
        
        // 重新創建控制面板
        panelState.isMinimized = false;
        GM_setValue('panel_minimized', false);
        
        createControlPanel();
        
        debugLog('控制面板已展開');
        updateStatus('控制面板已展開');
    }
    
    // 智能展開面板 - 自動計算最佳位置避免屏幕邊緣遮擋
    function expandPanelSmart() {
        const floatingContainer = safeQuerySelector('#unified-floating-container');
        if (!floatingContainer) {
            debugLog('未找到浮動按鈕容器，使用標準展開');
            expandPanel();
            return;
        }
        
        // 計算面板尺寸 (估算值)
        const panelWidth = 280; // 實際寬度 + padding
        const panelHeight = 400; // 估算高度
        const margin = 10; // 邊緣安全間距
        
        // 獲取當前按鈕位置
        const buttonRect = floatingContainer.getBoundingClientRect();
        const buttonCenterX = buttonRect.left + buttonRect.width / 2;
        const buttonCenterY = buttonRect.top + buttonRect.height / 2;
        
        // 獲取視窗尺寸
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 計算智能位置
        let optimalTop, optimalRight;
        
        // 垂直位置計算
        if (buttonCenterY < viewportHeight / 2) {
            // 按鈕在上半部分，面板放在下方
            optimalTop = Math.min(
                buttonRect.bottom + margin,
                viewportHeight - panelHeight - margin
            );
        } else {
            // 按鈕在下半部分，面板放在上方
            optimalTop = Math.max(
                buttonRect.top - panelHeight - margin,
                margin
            );
        }
        
        // 水平位置計算
        if (buttonCenterX < viewportWidth / 2) {
            // 按鈕在左半部分，面板放在右側，使用 right 值
            const leftPosition = Math.min(
                buttonRect.right + margin,
                viewportWidth - panelWidth - margin
            );
            optimalRight = viewportWidth - leftPosition - panelWidth;
        } else {
            // 按鈕在右半部分，面板放在左側
            const rightPosition = Math.max(
                buttonRect.left - panelWidth - margin,
                margin
            );
            optimalRight = viewportWidth - rightPosition - panelWidth;
        }
        
        // 邊界檢查和修正
        optimalTop = Math.max(margin, Math.min(optimalTop, viewportHeight - panelHeight - margin));
        optimalRight = Math.max(margin, Math.min(optimalRight, viewportWidth - panelWidth - margin));
        
        // 更新面板位置
        panelState.position = {
            top: Math.round(optimalTop),
            right: Math.round(optimalRight)
        };
        
        // 保存新位置
        GM_setValue('panel_position', JSON.stringify(panelState.position));
        
        // 移除浮動按鈕
        const floatingButton = safeQuerySelector('#unified-floating-button');
        if (floatingButton) {
            floatingButton.remove();
        }
        
        // 重新創建控制面板
        panelState.isMinimized = false;
        GM_setValue('panel_minimized', false);
        
        createControlPanel();
        
        debugLog('智能展開面板完成', {
            buttonPosition: { x: buttonCenterX, y: buttonCenterY },
            panelPosition: panelState.position,
            viewport: { width: viewportWidth, height: viewportHeight }
        });
        updateStatus('控制面板已智能展開');
    }

    function updateControlPanel() {
        const statusDiv = safeQuerySelector('#current-status');
        if (statusDiv) {
            let statusText = '';
            // 即時更新點讚狀態
            canLikeMore(); // 這會自動檢查和重置時間
            const likingStatus = getLikingStatus();
            
            if (globalState.isRunning) {
                statusText = `${globalState.currentPhase} | 話題${globalState.currentTopicId} | 已處理${globalState.processedCount} | 點讚${likingStatus.current}/${likingStatus.max}`;
            } else {
                let autoLikingInfo = '';
                if (globalState.config.liking.autoLiking.enabled) {
                    if (globalState.autoLiking.waitingForNextHour) {
                        const nextHour = new Date(getNextHourStart());
                        autoLikingInfo = ` | 等待: ${nextHour.getHours()}:00`;
                    } else if (globalState.autoLiking.isActive) {
                        autoLikingInfo = ` | 定時點讚: 運行中`;
                    } else {
                        autoLikingInfo = ` | 定時點讚: 停止`;
                    }
                }

                let lotteryInfo = '';
                if (globalState.config.lottery.enabled) {
                    const lotteryStatus = getLotteryStatus();
                    if (globalState.lottery.isActive) {
                        lotteryInfo = ` | 抽獎: ${lotteryStatus.hourlyCount}/${lotteryStatus.maxHourly}`;
                    } else {
                        lotteryInfo = ` | 抽獎: 停止`;
                    }
                }

                statusText = `待機中 | 點讚: ${likingStatus.remaining}/${likingStatus.max}${autoLikingInfo}${lotteryInfo}`;
            }
            statusDiv.textContent = statusText;
            debugLog('控制面板狀態更新:', statusText);
        }
    }

    // === 配置對話框 ===
    function showConfigDialog() {
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            z-index: 10001;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
        `;

        const config = globalState.config;
        dialog.innerHTML = `
            <h3 style="margin-top: 0; text-align: center;">腳本配置</h3>
            
            <div style="margin-bottom: 15px;">
                <h4>瀏覽設置</h4>
                <label>話題停留時間(毫秒): <input type="number" id="topic-stay-time" value="${config.browse.topicStayTime}"></label><br>
                <label>瀏覽方向: 
                    <select id="browse-direction">
                        <option value="+" ${config.browse.processingDirection === '+' ? 'selected' : ''}>遞增</option>
                        <option value="-" ${config.browse.processingDirection === '-' ? 'selected' : ''}>遞減</option>
                    </select>
                </label><br>
                <label>步進值: <input type="number" id="browse-step" value="${config.browse.processingStep}"></label>
            </div>

            <div style="margin-bottom: 15px;">
                <h4>閱讀設置</h4>
                <label><input type="checkbox" id="reading-enabled" ${config.reading.enabled ? 'checked' : ''}> 啟用閱讀模擬</label><br>
                <label><input type="checkbox" id="reading-complete" ${config.reading.ensureComplete ? 'checked' : ''}> 確保完整閱讀所有回復</label><br>
                <label><input type="checkbox" id="reading-auto-start" ${config.reading.autoStart ? 'checked' : ''}> 自動啟動閱讀模擬（獨立於話題瀏覽）</label><br>
                <label>頁面載入後延遲時間(ms): <input type="number" id="reading-delay" value="${config.reading.delayAfterPageLoad}" min="1000" max="10000"></label><br>
                <label>基礎延遲(毫秒): <input type="number" id="read-base-delay" value="${config.reading.baseDelay}"></label><br>
                <label>隨機延遲範圍: <input type="number" id="read-random-range" value="${config.reading.randomDelayRange}"></label><br>
                <label>最小請求量: <input type="number" id="read-min-req" value="${config.reading.minReqSize}"></label><br>
                <label>最大請求量: <input type="number" id="read-max-req" value="${config.reading.maxReqSize}"></label>
            </div>

            <div style="margin-bottom: 15px;">
                <h4>點讚設置</h4>
                <label><input type="checkbox" id="liking-enabled" ${config.liking.enabled ? 'checked' : ''}> 啟用自動點讚</label><br>
                <label>每話題最多點讚數: <input type="number" id="max-likes-per-topic" value="${config.liking.maxLikesPerTopic || 5}" min="1" max="20"></label><br>
                <label>最小延遲(毫秒): <input type="number" id="like-min-delay" value="${config.liking.minDelay}"></label><br>
                <label>最大延遲(毫秒): <input type="number" id="like-max-delay" value="${config.liking.maxDelay}"></label><br>
                
                <h5 style="margin-top: 15px; margin-bottom: 10px; color: #007bff;">定時點讚系統</h5>
                <label><input type="checkbox" id="auto-liking-enabled" ${config.liking.autoLiking.enabled ? 'checked' : ''}> 啟用每小時自動點讚</label><br>
                <label>每小時點讚目標: <input type="number" id="hourly-target" value="${config.liking.autoLiking.hourlyTarget}" min="5" max="50"></label><br>
                <label>檢查間隔(秒): <input type="number" id="check-interval" value="${config.liking.autoLiking.checkInterval/1000}" min="30" max="300"></label><br>
                <label>話題間最小延遲(秒): <input type="number" id="min-topic-delay" value="${config.liking.autoLiking.minTopicDelay/1000}" min="1" max="30"></label><br>
                <label>話題間最大延遲(秒): <input type="number" id="max-topic-delay" value="${config.liking.autoLiking.maxTopicDelay/1000}" min="5" max="60"></label><br>
                <label><input type="checkbox" id="random-start" ${config.liking.autoLiking.randomStart ? 'checked' : ''}> 隨機化開始時間</label>
            </div>

            <div style="margin-bottom: 15px;">
                <h4 style="color: #ff6b35;">抽獎自動回覆系統</h4>
                <label><input type="checkbox" id="lottery-enabled" ${config.lottery.enabled ? 'checked' : ''}> 啟用抽獎自動回覆</label><br>
                <label>檢查間隔(分鐘): <input type="number" id="lottery-check-interval" value="${config.lottery.checkInterval/60000}" min="1" max="60"></label><br>
                <label>每小時最大回覆數: <input type="number" id="lottery-max-replies" value="${config.lottery.maxRepliesPerHour}" min="1" max="30"></label><br>
                <label>回覆最小延遲(秒): <input type="number" id="lottery-min-delay" value="${config.lottery.replyDelay.min/1000}" min="1" max="30"></label><br>
                <label>回覆最大延遲(秒): <input type="number" id="lottery-max-delay" value="${config.lottery.replyDelay.max/1000}" min="5" max="60"></label><br>
                <label><input type="checkbox" id="lottery-only-today" ${config.lottery.onlyToday ? 'checked' : ''}> 只回覆今天的話題</label><br>
                <div style="margin-top: 10px;">
                    <label style="display: block; margin-bottom: 5px;">回覆內容 (每行一個):</label>
                    <textarea id="lottery-replies" style="width: 100%; height: 100px; font-size: 12px;" placeholder="参与一下\n来抽奖啦\n抽抽抽抽">${config.lottery.replies.join('\n')}</textarea>
                </div>
            </div>

            <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                <button id="save-config" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">保存</button>
                <button id="reset-config" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">重置</button>
                <button id="close-config" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">關閉</button>
            </div>
        `;

        dialog.querySelector('#save-config').addEventListener('click', () => {
            try {
                // 保存配置
                const oldConfig = JSON.parse(JSON.stringify(globalState.config)); // 備份舊配置
                
                globalState.config.browse.topicStayTime = parseInt(dialog.querySelector('#topic-stay-time').value);
                globalState.config.browse.processingDirection = dialog.querySelector('#browse-direction').value;
                globalState.config.browse.processingStep = parseInt(dialog.querySelector('#browse-step').value);
                
                globalState.config.reading.enabled = dialog.querySelector('#reading-enabled').checked;
                globalState.config.reading.ensureComplete = dialog.querySelector('#reading-complete').checked;
                globalState.config.reading.autoStart = dialog.querySelector('#reading-auto-start').checked;
                globalState.config.reading.delayAfterPageLoad = parseInt(dialog.querySelector('#reading-delay').value);
                globalState.config.reading.baseDelay = parseInt(dialog.querySelector('#read-base-delay').value);
                globalState.config.reading.randomDelayRange = parseInt(dialog.querySelector('#read-random-range').value);
                globalState.config.reading.minReqSize = parseInt(dialog.querySelector('#read-min-req').value);
                globalState.config.reading.maxReqSize = parseInt(dialog.querySelector('#read-max-req').value);
                
                globalState.config.liking.enabled = dialog.querySelector('#liking-enabled').checked;
                globalState.config.liking.maxLikesPerTopic = parseInt(dialog.querySelector('#max-likes-per-topic').value);
                globalState.config.liking.minDelay = parseInt(dialog.querySelector('#like-min-delay').value);
                globalState.config.liking.maxDelay = parseInt(dialog.querySelector('#like-max-delay').value);
                
                // 定時點讚設定
                const wasAutoLikingEnabled = globalState.config.liking.autoLiking.enabled;
                globalState.config.liking.autoLiking.enabled = dialog.querySelector('#auto-liking-enabled').checked;
                globalState.config.liking.autoLiking.hourlyTarget = parseInt(dialog.querySelector('#hourly-target').value);
                globalState.config.liking.autoLiking.checkInterval = parseInt(dialog.querySelector('#check-interval').value) * 1000;
                globalState.config.liking.autoLiking.minTopicDelay = parseInt(dialog.querySelector('#min-topic-delay').value) * 1000;
                globalState.config.liking.autoLiking.maxTopicDelay = parseInt(dialog.querySelector('#max-topic-delay').value) * 1000;
                globalState.config.liking.autoLiking.randomStart = dialog.querySelector('#random-start').checked;

                // 抽獎系統設定
                const wasLotteryEnabled = globalState.config.lottery.enabled;
                globalState.config.lottery.enabled = dialog.querySelector('#lottery-enabled').checked;
                globalState.config.lottery.checkInterval = parseInt(dialog.querySelector('#lottery-check-interval').value) * 60000;
                globalState.config.lottery.maxRepliesPerHour = parseInt(dialog.querySelector('#lottery-max-replies').value);
                globalState.config.lottery.replyDelay.min = parseInt(dialog.querySelector('#lottery-min-delay').value) * 1000;
                globalState.config.lottery.replyDelay.max = parseInt(dialog.querySelector('#lottery-max-delay').value) * 1000;
                globalState.config.lottery.onlyToday = dialog.querySelector('#lottery-only-today').checked;

                // 處理回覆內容
                const repliesText = dialog.querySelector('#lottery-replies').value.trim();
                if (repliesText) {
                    globalState.config.lottery.replies = repliesText.split('\n').filter(line => line.trim()).map(line => line.trim());
                }

                // 嘗試保存配置
                const saveSuccess = saveConfig();
                
                if (saveSuccess) {
                    updateStatus('配置已成功保存');
                    debugLog('配置更新成功');
                    
                    // 如果腦本正在運行，顯示提示但不立即應用
                    if (globalState.isRunning) {
                        updateStatus('配置已保存，將在下次頁面載入時生效');
                        alert('配置已保存！\n\n由於腦本正在自動瀏覽，新配置將在下一個話題頁面自動生效。');
                    } else {
                        // 腦本未運行，可以立即應用更新
                        
                        // 處理自動點讚系統的啟動/停止
                        if (globalState.config.liking.autoLiking.enabled && !wasAutoLikingEnabled) {
                            startAutoLikingSystem();
                        } else if (!globalState.config.liking.autoLiking.enabled && wasAutoLikingEnabled) {
                            stopAutoLikingSystem();
                        } else if (globalState.config.liking.autoLiking.enabled) {
                            // 重新啟動以應用新設定
                            stopAutoLikingSystem();
                            setTimeout(() => startAutoLikingSystem(), 1000);
                        }

                        // 處理抽獎系統的啟動/停止
                        if (globalState.config.lottery.enabled && !wasLotteryEnabled) {
                            startLotterySystem();
                        } else if (!globalState.config.lottery.enabled && wasLotteryEnabled) {
                            stopLotterySystem();
                        } else if (globalState.config.lottery.enabled) {
                            // 重新啟動以應用新設定
                            stopLotterySystem();
                            setTimeout(() => startLotterySystem(), 1000);
                        }

                        updateStatus('配置已保存並即時生效');
                    }
                    
                    updateControlPanel(); // 更新控制面板
                    dialog.remove();
                } else {
                    // 保存失敗，還原配置
                    globalState.config = oldConfig;
                    alert('配置保存失敗，請重試');
                    updateStatus('配置保存失敗');
                }
                
            } catch (error) {
                debugLog('保存配置時出錯:', error);
                alert('配置保存出錯: ' + error.message);
                updateStatus('配置保存出錯');
            }
        });

        dialog.querySelector('#reset-config').addEventListener('click', () => {
            if (confirm('確定要重置為默認配置嗎？')) {
                globalState.config = { ...DEFAULT_CONFIG };
                saveConfig();
                updateStatus('配置已重置');
                dialog.remove();
            }
        });

        dialog.querySelector('#close-config').addEventListener('click', () => {
            dialog.remove();
        });

        document.body.appendChild(dialog);
    }

    // === 狀態報告 ===
    function showStatusReport() {
        const likingStatus = getLikingStatus();
        const resetTime = Math.ceil(likingStatus.resetIn / (1000 * 60)); // 轉換為分鐘
        
        const report = `
=== Linux.do 一體化腳本狀態報告 ===

🔄 運行狀態: ${globalState.isRunning ? '運行中' : '已停止'}
📊 當前階段: ${globalState.currentPhase}
🎯 當前話題ID: ${globalState.currentTopicId || 'N/A'}
📈 已處理話題數: ${globalState.processedCount}

⚙️ 功能狀態:
  • 瀏覽功能: 啟用
  • 閱讀模擬: ${globalState.config.reading.enabled ? '啟用' : '停用'}
  • 自動點讚: ${globalState.config.liking.enabled ? '啟用' : '停用'}
  • 工作流模式: ${globalState.config.global.workflowMode ? '啟用' : '停用'}

❤️ 點讚限制狀態:
  • 當前小時已點讚: ${likingStatus.current}/${likingStatus.max}
  • 剩餘配額: ${likingStatus.remaining}
  • 重置時間: ${resetTime > 0 ? `${resetTime}分鐘後` : '已可重置'}
  • 每話題最多點讚: ${globalState.config.liking.maxLikesPerTopic || 5}個

⏰ 定時點讚狀態:
  • 系統狀態: ${globalState.config.liking.autoLiking.enabled ? '啟用' : '停用'}
  • 運行狀態: ${globalState.autoLiking.isActive ? '運行中' : '未運行'}
  • 每小時目標: ${globalState.config.liking.autoLiking.hourlyTarget}次
  • 等待狀態: ${globalState.autoLiking.waitingForNextHour ? '等待下一小時' : '正常'}
  • 下次重置: ${new Date(getNextHourStart()).toLocaleTimeString()}

📝 最近日誌:
${logBuffer.slice(-5).map(log => `  • ${log.message}`).join('\n')}

=== 報告結束 ===
        `;
        
        alert(report);
    }

    // === 閱讀模擬功能 ===
    let autoReadingTimer = null;
    
    // 自動啟動閱讀模擬功能
    function startAutoReading() {
        // 清除之前的定時器
        if (autoReadingTimer) {
            clearTimeout(autoReadingTimer);
            autoReadingTimer = null;
        }
        
        // 檢查是否啟用自動閱讀
        if (!globalState.config.reading.autoStart || !globalState.config.reading.enabled) {
            debugLog('自動閱讀未啟用，跳過');
            return;
        }
        
        // 檢查是否在話題頁面
        if (!isTopicPage()) {
            debugLog('不在話題頁面，跳過自動閱讀');
            return;
        }
        
        // 如果正在運行一體化腦本，不啟動自動閱讀
        if (globalState.isRunning) {
            debugLog('一體化腦本運行中，不啟動自動閱讀');
            return;
        }
        
        const delay = globalState.config.reading.delayAfterPageLoad;
        debugLog(`設定自動閱讀定時器: ${delay}ms`);
        
        autoReadingTimer = setTimeout(async () => {
            try {
                debugLog('開始自動閱讀模擬');
                updateStatus('自動閱讀模擬啟動中...');
                await simulateReading();
                updateStatus('自動閱讀模擬完成');
            } catch (error) {
                debugLog('自動閱讀模擬出錯:', error);
                updateStatus('自動閱讀模擬出錯');
            }
        }, delay);
    }
    
    // 停止自動閱讀
    function stopAutoReading() {
        if (autoReadingTimer) {
            clearTimeout(autoReadingTimer);
            autoReadingTimer = null;
            debugLog('自動閱讀定時器已清除');
        }
    }
    
    async function simulateReading() {
        if (!globalState.config.reading.enabled) {
            debugLog('閱讀模擬已停用，跳過');
            return;
        }

        try {
            debugLog('開始閱讀模擬');
            globalState.currentPhase = 'reading';
            updateControlPanel();
            updateStatus('正在模擬閱讀...');

            // 獲取頁面信息
            const topicID = getCurrentTopicIdFromUrl();
            if (!topicID) {
                debugLog('無法獲取話題ID，跳過閱讀模擬');
                return;
            }

            const repliesElement = safeQuerySelector("div[class=timeline-replies]");
            const csrfElement = safeQuerySelector("meta[name=csrf-token]");

            if (!repliesElement || !csrfElement) {
                debugLog('無法獲取頁面信息，跳過閱讀模擬');
                return;
            }

            const repliesInfo = repliesElement.textContent.trim();
            const [currentPosition, totalReplies] = repliesInfo.split("/").map(part => parseInt(part.trim(), 10));
            const csrfToken = csrfElement.getAttribute("content");

            if (isNaN(totalReplies) || totalReplies <= 0) {
                debugLog('無效的回復數量，跳過閱讀模擬');
                return;
            }

            debugLog(`開始完整閱讀模擬，總回復數: ${totalReplies}，當前位置: ${currentPosition}`);
            
            // 執行完整的閱讀模擬 - 分批處理所有回復
            const config = globalState.config.reading;
            let processedPosts = 0;
            let currentPos = 1; // 從第1個開始，確保完整覆蓋
            
            while (currentPos <= totalReplies && globalState.isRunning) {
                const batchSize = Math.min(config.maxReqSize, totalReplies - currentPos + 1);
                const endPosition = currentPos + batchSize - 1;
                
                debugLog(`處理批次: ${currentPos}-${endPosition} (共${batchSize}個回復)`);
                updateStatus(`閱讀模擬中... (${currentPos}-${endPosition}/${totalReplies})`);

                let batchSuccess = false;
                let retryCount = 0;
                const maxRetries = 3;

                // 重試機制處理當前批次
                while (!batchSuccess && retryCount < maxRetries && globalState.isRunning) {
                    const params = new URLSearchParams();
                    
                    // 為當前批次的每個位置添加閱讀時間
                    for (let i = currentPos; i <= endPosition; i++) {
                        const readTime = Math.floor(Math.random() * (config.maxReadTime - config.minReadTime + 1)) + config.minReadTime;
                        params.append(`timings[${i}]`, readTime.toString());
                    }

                    // 計算總閱讀時間
                    const topicTime = batchSize * Math.floor(Math.random() * (config.maxReadTime - config.minReadTime + 1)) + config.minReadTime;
                    params.append('topic_time', topicTime.toString());
                    params.append('topic_id', topicID.toString());

                    try {
                        if (retryCount > 0) {
                            debugLog(`重試批次 ${currentPos}-${endPosition}，第 ${retryCount} 次重試`);
                            updateStatus(`重試閱讀 (${currentPos}-${endPosition}) 第${retryCount}次...`);
                        }

                        // 發送當前批次的請求
                        const response = await fetch("https://linux.do/topics/timings", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                                "X-CSRF-Token": csrfToken,
                                "X-Requested-With": "XMLHttpRequest"
                            },
                            body: params,
                            credentials: "include"
                        });

                        if (response.ok) {
                            processedPosts += batchSize;
                            batchSuccess = true;
                            debugLog(`批次 ${currentPos}-${endPosition} 閱讀模擬成功`);
                            
                            // 批次間延遲
                            const delay = config.baseDelay + Math.random() * config.randomDelayRange;
                            debugLog(`批次完成，延遲 ${delay}ms 後繼續`);
                            await sleep(delay);
                            
                        } else {
                            retryCount++;
                            debugLog(`批次 ${currentPos}-${endPosition} 閱讀模擬失敗: HTTP ${response.status}，準備重試`);
                            if (retryCount < maxRetries) {
                                await sleep(2000 * retryCount); // 遞增延遲
                            }
                        }
                    } catch (error) {
                        retryCount++;
                        debugLog(`批次 ${currentPos}-${endPosition} 請求出錯:`, error);
                        if (retryCount < maxRetries) {
                            await sleep(2000 * retryCount); // 遞增延遲
                        }
                    }
                }

                // 如果重試後仍然失敗，記錄但繼續處理下一批次
                if (!batchSuccess) {
                    debugLog(`批次 ${currentPos}-${endPosition} 最終失敗，跳過此批次`);
                    updateStatus(`批次 ${currentPos}-${endPosition} 失敗，繼續下一批次...`);
                    await sleep(1000);
                }
                
                // 移動到下一批次
                currentPos = endPosition + 1;
                
                // 檢查是否應該停止
                if (!globalState.isRunning) {
                    debugLog('閱讀模擬被中斷');
                    break;
                }
            }
            
            debugLog(`閱讀模擬完成，共處理 ${processedPosts}/${totalReplies} 個回復`);
            updateStatus(`閱讀模擬完成 (${processedPosts}/${totalReplies})`)

        } catch (error) {
            debugLog('閱讀模擬出錯:', error);
            // 出錯時也要有延遲，避免過快跳轉
            await sleep(1000);
        }
        
        debugLog('閱讀模擬函數執行完成');
    }

    // === 自動點讚定時系統 ===
    
    // 計算當前小時的開始時間
    function getCurrentHourStart() {
        const now = new Date();
        now.setMinutes(0, 0, 0); // 設定為整點
        return now.getTime();
    }
    
    // 計算下一小時的開始時間
    function getNextHourStart() {
        const now = new Date();
        now.setHours(now.getHours() + 1, 0, 0, 0);
        return now.getTime();
    }
    
    // 更新自動點讚狀態
    function updateAutoLikingStatus() {
        const currentHour = getCurrentHourStart();
        
        // 如果是新的小時，重置計數
        if (currentHour !== globalState.autoLiking.currentHourStart) {
            globalState.autoLiking.currentHourStart = currentHour;
            globalState.liking.hourlyCount = 0;
            globalState.liking.lastResetTime = Date.now();
            globalState.autoLiking.waitingForNextHour = false;
            
            saveLikingLimits();
            debugLog('新小時開始，點讚計數已重置');
        }
    }
    
    // 啟動自動點讚定時系統
    function startAutoLikingSystem() {
        if (!globalState.config.liking.autoLiking.enabled) {
            debugLog('自動點讚系統未啟用');
            return;
        }
        
        if (globalState.autoLiking.isActive) {
            debugLog('自動點讚系統已在運行');
            return;
        }
        
        globalState.autoLiking.isActive = true;
        globalState.autoLiking.currentHourStart = getCurrentHourStart();
        updateAutoLikingStatus();
        
        // 設定檢查定時器 - 每分鐘檢查一次
        const checkInterval = globalState.config.liking.autoLiking.checkInterval;
        globalState.autoLiking.checkTimer = setInterval(() => {
            performAutoLiking();
        }, checkInterval);
        
        debugLog('自動點讚系統已啟動，檢查間隔:', checkInterval + 'ms');
        updateStatus('自動點讚系統已啟動');
        
        // 立即執行一次檢查
        setTimeout(() => performAutoLiking(), 5000);
    }
    
    // 停止自動點讚系統
    function stopAutoLikingSystem() {
        if (globalState.autoLiking.checkTimer) {
            clearInterval(globalState.autoLiking.checkTimer);
            globalState.autoLiking.checkTimer = null;
        }
        
        if (globalState.autoLiking.timer) {
            clearTimeout(globalState.autoLiking.timer);
            globalState.autoLiking.timer = null;
        }
        
        globalState.autoLiking.isActive = false;
        globalState.autoLiking.waitingForNextHour = false;
        
        debugLog('自動點讚系統已停止');
        updateStatus('自動點讚系統已停止');
    }
    
    // 檢查是否需要執行自動點讚
    function shouldPerformAutoLiking() {
        // 更新狀態（檢查是否進入新小時）
        updateAutoLikingStatus();
        
        // 檢查是否已達成目標
        const hourlyTarget = globalState.config.liking.autoLiking.hourlyTarget;
        if (globalState.liking.hourlyCount >= hourlyTarget) {
            if (!globalState.autoLiking.waitingForNextHour) {
                globalState.autoLiking.waitingForNextHour = true;
                const nextHour = new Date(getNextHourStart());
                debugLog(`小時點讚目標已達成 (${globalState.liking.hourlyCount}/${hourlyTarget})，等待下一小時: ${nextHour.toLocaleTimeString()}`);
                updateStatus(`小時點讚完成 (${globalState.liking.hourlyCount}/${hourlyTarget})，等待下一小時`);
            }
            return false;
        }
        
        // 檢查是否在話題頁面
        if (!isTopicPage()) {
            debugLog('不在話題頁面，跳過自動點讚');
            return false;
        }
        
        // 檢查是否有其他任務在運行
        if (globalState.isRunning) {
            debugLog('一體化腦本運行中，跳過自動點讚');
            return false;
        }
        
        return true;
    }
    
    // 執行自動點讚
    async function performAutoLiking() {
        if (!shouldPerformAutoLiking()) {
            return;
        }
        
        try {
            const hourlyTarget = globalState.config.liking.autoLiking.hourlyTarget;
            const remaining = hourlyTarget - globalState.liking.hourlyCount;
            
            debugLog(`開始自動點讚檢查，剩餘: ${remaining}/${hourlyTarget}`);
            updateStatus(`自動點讚中... 剩餘: ${remaining}/${hourlyTarget}`);
            
            // 執行點讚
            await autoLike();
            
            // 檢查是否完成目標
            if (globalState.liking.hourlyCount >= hourlyTarget) {
                globalState.autoLiking.waitingForNextHour = true;
                const nextHour = new Date(getNextHourStart());
                debugLog(`小時點讚目標已達成！等待下一小時: ${nextHour.toLocaleTimeString()}`);
                updateStatus(`小時點讚完成！下一小時: ${nextHour.toLocaleTimeString()}`);
                return;
            }
            
            // 計算下次檢查的隨機延遲
            const minDelay = globalState.config.liking.autoLiking.minTopicDelay;
            const maxDelay = globalState.config.liking.autoLiking.maxTopicDelay;
            const randomDelay = Math.random() * (maxDelay - minDelay) + minDelay;
            
            debugLog(`下次自動點讚將在 ${Math.round(randomDelay/1000)} 秒後執行`);
            
            // 設定下次執行的延遲
            globalState.autoLiking.timer = setTimeout(() => {
                performAutoLiking();
            }, randomDelay);
            
        } catch (error) {
            debugLog('自動點讚執行出錯:', error);
            updateStatus('自動點讚出錯');
        }
    }
    
    // === 自動點讚功能 ===
    async function autoLike() {
        if (!globalState.config.liking.enabled) {
            debugLog('自動點讚已停用，跳過');
            return;
        }

        // 檢查小時限制
        if (!canLikeMore()) {
            const status = getLikingStatus();
            const resetTime = Math.ceil(status.resetIn / (1000 * 60)); // 轉換為分鐘
            debugLog(`已達到小時點讚限制 (${status.current}/${status.max})，${resetTime}分鐘後重置`);
            updateStatus(`點讚已達限制 (${status.current}/${status.max})，${resetTime}分鐘後重置`);
            return;
        }

        try {
            debugLog('開始自動點讚');
            globalState.currentPhase = 'liking';
            updateControlPanel();
            
            const status = getLikingStatus();
            updateStatus(`正在自動點讚... (剩餘: ${status.remaining}/${status.max})`);

            let likedCount = 0;
            const maxLikes = Math.min(
                globalState.config.liking.maxLikesPerTopic || 5, // 每個話題最多點讚數
                status.remaining // 不能超過小時限制
            );
            
            if (maxLikes <= 0) {
                debugLog('小時點讚配額已用完');
                updateStatus('小時點讚配額已用完');
                return;
            }
            
            // 獲取所有愛心按鈕
            const buttons = document.querySelectorAll('.discourse-reactions-reaction-button');
            const likeableButtons = [];

            // 篩選出可以點讚的按鈕（未點擊的愛心按鈕）
            for (const button of buttons) {
                if (!button.classList.contains('already_clicked') && 
                    button.innerHTML.includes("d-icon-far-heart")) {
                    likeableButtons.push(button);
                }
            }

            debugLog(`找到 ${likeableButtons.length} 個可點讚按鈕，將點讚前 ${Math.min(maxLikes, likeableButtons.length)} 個`);

            // 點讚前面最多maxLikes個
            const buttonsToLike = likeableButtons.slice(0, maxLikes);
            
            for (let i = 0; i < buttonsToLike.length; i++) {
                if (!globalState.isRunning) break;
                
                // 再次檢查小時限制
                if (!canLikeMore()) {
                    debugLog('點讚過程中達到小時限制，停止點讚');
                    break;
                }
                
                const button = buttonsToLike[i];
                button.classList.add('already_clicked');
                button.click();
                likedCount++;
                
                // 記錄點讚到限制系統
                recordLike(1);
                
                debugLog(`點擊第 ${likedCount} 個愛心按鈕成功`);
                const currentStatus = getLikingStatus();
                updateStatus(`自動點讚中... (${likedCount}/${buttonsToLike.length}) 剩餘: ${currentStatus.remaining}`);

                // 如果不是最後一個按鈕，才需要延遲
                if (i < buttonsToLike.length - 1) {
                    const delay = getRandomDelay(
                        globalState.config.liking.minDelay,
                        globalState.config.liking.maxDelay
                    );
                    await sleep(delay);
                }
            }

            const finalStatus = getLikingStatus();
            debugLog(`自動點讚完成，共點讚 ${likedCount} 次，剩餘配額: ${finalStatus.remaining}`);
            updateStatus(`點讚完成 (${likedCount}) 剩餘: ${finalStatus.remaining}/${finalStatus.max}`);

        } catch (error) {
            debugLog('自動點讚出錯:', error);
        }
        
        debugLog('自動點讚函數執行完成');
    }

    // === 話題瀏覽功能 ===
    async function browseTopic() {
        try {
            debugLog('開始處理當前話題');
            globalState.currentPhase = 'browsing';
            updateControlPanel();

            const currentTopicId = getCurrentTopicIdFromUrl();
            if (currentTopicId && currentTopicId !== globalState.currentTopicId) {
                globalState.currentTopicId = currentTopicId;
            }

            updateStatus(`正在瀏覽話題 ${globalState.currentTopicId}`);

            // 等待頁面穩定
            await sleep(1000);

            // 檢查是否為404頁面
            if (is404Page()) {
                debugLog(`話題 ${globalState.currentTopicId} 不存在，跳到下一個`);
                updateStatus('話題不存在，跳到下一個');
                await sleep(2000);
                return processNextTopic();
            }

            // 如果啟用工作流模式，執行所有功能
            if (globalState.config.global.workflowMode && globalState.config.global.enableAllFeatures) {
                // 1. 閱讀模擬 - 確保完成
                debugLog('開始執行閱讀模擬');
                await simulateReading();
                debugLog('閱讀模擬完成，開始點讚');
                
                // 2. 自動點讚 - 等待閱讀模擬完成後執行
                await autoLike();
                debugLog('點讚完成');
                
                // 完成所有功能後直接跳轉，不等待
                updateStatus('話題處理完成，準備跳轉到下一個話題');
                await sleep(1000); // 只等待1秒讓用戶看到狀態
            } else {
                // 如果不是工作流模式，使用原來的等待時間
                const stayTime = globalState.config.browse.topicStayTime;
                updateStatus(`話題瀏覽完成，${Math.ceil(stayTime/1000)} 秒後跳轉`);
                
                let remainingTime = Math.ceil(stayTime / 1000);
                const countdownInterval = setInterval(() => {
                    if (!globalState.isRunning) {
                        clearInterval(countdownInterval);
                        return;
                    }
                    updateStatus(`等待跳轉... ${remainingTime--} 秒`);
                    if (remainingTime < 0) {
                        clearInterval(countdownInterval);
                    }
                }, 1000);

                await sleep(stayTime);
                clearInterval(countdownInterval);
            }

            // 處理下一個話題
            if (globalState.isRunning) {
                saveState(); // 在跳轉前保存狀態
                processNextTopic();
            }

        } catch (error) {
            debugLog('瀏覽話題出錯:', error);
            updateStatus('處理出錯，繼續下一個');
            setTimeout(() => {
                if (globalState.isRunning) {
                    processNextTopic();
                }
            }, 3000);
        }
    }

    // === 404頁面檢測 ===
    function is404Page() {
        try {
            // 檢查URL
            if (window.location.href.includes('/404')) {
                return true;
            }

            // 檢查標題
            const title = document.title || '';
            if (title.includes('404') || title.includes('Not Found') || 
                title.includes('錯誤') || title.includes('Error')) {
                return true;
            }

            // 檢查頁面內容
            const bodyText = document.body ? document.body.textContent || '' : '';
            const errorTexts = [
                '頁面不存在', 'Page not found', '找不到頁面',
                'Topic not found', '話題不存在', '無法找到',
                'Oops! That page doesn\'t exist', '抱歉，頁面不存在',
                '抱歉！這個頁面不存在或者是私密的', '這個頁面不存在或者是私密的',
                'Sorry, that page doesn\'t exist', 'Page doesn\'t exist'
            ];

            for (const errorText of errorTexts) {
                if (bodyText.toLowerCase().includes(errorText.toLowerCase())) {
                    return true;
                }
            }

            return false;
        } catch (error) {
            debugLog('404檢測出錯:', error);
            return false;
        }
    }

    // === 下一話題處理 ===
    function processNextTopic() {
        debugLog('準備處理下一個話題');
        
        if (!globalState.isRunning) {
            debugLog('腳本已停止，取消處理');
            return;
        }

        // 更新計數器
        globalState.processedCount++;
        
        // 計算下一個話題ID
        const config = globalState.config.browse;
        if (config.processingDirection === "+") {
            globalState.currentTopicId += config.processingStep;
        } else {
            globalState.currentTopicId -= config.processingStep;
            if (globalState.currentTopicId < 1) {
                globalState.currentTopicId = 1;
                globalState.config.browse.processingDirection = "+"; // 轉為遞增
                debugLog('已達到最小ID，轉為遞增方向');
            }
        }

        debugLog(`下一個話題ID: ${globalState.currentTopicId}`);
        updateStatus(`導航到話題 ${globalState.currentTopicId}`);

        // 導航到下一個話題
        setTimeout(() => {
            window.location.href = `https://linux.do/t/topic/${globalState.currentTopicId}`;
        }, 1000);
    }

    // === 腳本控制 ===
    async function startUnifiedScript() {
        if (globalState.isRunning) {
            updateStatus('腳本已在運行中');
            return;
        }

        try {
            debugLog('啟動一體化腳本');
            
            // 讓用戶選擇啟動模式
            const mode = prompt(
                "請選擇啟動模式：\n" +
                "1 = 從當前話題開始（如果在話題頁面）\n" +
                "2 = 自定義起始話題ID\n" +
                "3 = 從最新話題開始", 
                "1"
            );
            
            if (!mode || !["1", "2", "3"].includes(mode)) {
                updateStatus('已取消啟動');
                return;
            }

            // 讓用戶選擇瀏覽方向
            const direction = prompt(
                "請選擇瀏覽方向：\n" +
                "+ = 遞增（ID由小到大）\n" +
                "- = 遞減（ID由大到小）", 
                "+"
            );
            
            if (direction === null) {
                updateStatus('已取消啟動');
                return;
            }

            globalState.config.browse.processingDirection = direction.trim() === "-" ? "-" : "+";
            
            // 讓用戶設置步進值
            const step = prompt("請輸入話題ID間隔（步進值）:", "1");
            if (step === null) {
                updateStatus('已取消啟動');
                return;
            }
            
            if (!/^\d+$/.test(step.trim()) || parseInt(step.trim()) < 1) {
                alert("請輸入大於或等於1的正整數");
                updateStatus('步進值無效');
                return;
            }
            
            globalState.config.browse.processingStep = parseInt(step.trim());
            saveConfig(); // 保存用戶選擇

            globalState.isRunning = true;
            globalState.processedCount = 0;

            if (mode === "1") {
                // 從當前話題開始
                if (isTopicPage()) {
                    const topicId = getCurrentTopicIdFromUrl();
                    if (topicId) {
                        globalState.currentTopicId = topicId;
                        updateStatus('從當前話題開始');
                        saveState(); // 保存狀態
                        await browseTopic();
                    } else {
                        updateStatus('無法獲取當前話題ID');
                        globalState.isRunning = false;
                    }
                } else {
                    alert("當前不在話題頁面，請選擇其他模式");
                    globalState.isRunning = false;
                    return;
                }
            } else if (mode === "2") {
                // 自定義起始話題
                const startId = prompt('請輸入起始話題ID:', '1');
                if (startId && /^\d+$/.test(startId.trim())) {
                    globalState.currentTopicId = parseInt(startId.trim());
                    updateStatus(`設置起始話題: ${globalState.currentTopicId}`);
                    saveState(); // 保存狀態
                    window.location.href = `https://linux.do/t/topic/${globalState.currentTopicId}`;
                } else {
                    globalState.isRunning = false;
                    updateStatus('未輸入有效的話題ID');
                }
            } else if (mode === "3") {
                // 從最新話題開始（簡化版）
                updateStatus('正在獲取最新話題...');
                // 這裡簡化處理，使用一個較大的ID作為起點
                globalState.currentTopicId = 180000; // 假設的最新話題ID範圍
                saveState(); // 保存狀態
                window.location.href = `https://linux.do/t/topic/${globalState.currentTopicId}`;
            }

        } catch (error) {
            debugLog('啟動腳本失敗:', error);
            globalState.isRunning = false;
            updateStatus('啟動失敗');
        }

        updateControlPanel();
    }

    function stopUnifiedScript() {
        debugLog('停止一體化腳本');
        globalState.isRunning = false;
        globalState.currentPhase = 'idle';
        clearState(); // 清除保存的狀態
        updateStatus('腳本已停止');
        updateControlPanel();
    }

    // === 頁面加載處理 ===
    async function onPageLoad() {
        try {
            debugLog('頁面加載完成，初始化腳本');
            
            // 載入配置和狀態
            loadConfig();
            loadLikingLimits(); // 載入點讚限制狀態
            const stateRestored = loadState();
            
            // 創建控制面板
            createControlPanel();
            createStatusDisplay();
            
            if (stateRestored && globalState.isRunning) {
                debugLog('狀態已恢復，腳本運行中');
                updateStatus(`狀態已恢復 - 話題${globalState.currentTopicId}`);
                updateControlPanel();

                // 如果在話題頁面，檢查是否應該繼續處理
                if (isTopicPage()) {
                    const currentTopicId = getCurrentTopicIdFromUrl();
                    debugLog('頁面話題ID檢查:', {
                        currentTopicId,
                        expectedTopicId: globalState.currentTopicId,
                        match: currentTopicId === globalState.currentTopicId
                    });

                    if (currentTopicId === globalState.currentTopicId) {
                        debugLog('在正確的話題頁面，繼續處理');
                        setTimeout(() => browseTopic(), 2000);
                    } else if (currentTopicId) {
                        debugLog('在不同的話題頁面，更新當前話題ID並繼續');
                        globalState.currentTopicId = currentTopicId;
                        saveState();
                        setTimeout(() => browseTopic(), 2000);
                    } else {
                        debugLog('無法獲取話題ID，跳到下一個');
                        setTimeout(() => processNextTopic(), 2000);
                    }
                } else {
                    debugLog('不在話題頁面，可能正在加載中');
                    updateStatus('等待頁面加載...');
                }
            } else {
                // 更新狀態
                updateStatus('腳本已載入');
                updateControlPanel();
                debugLog('沒有有效的運行狀態');
                
                // 如果在話題頁面且啟用了自動閱讀，啟動自動閱讀模擬
                if (isTopicPage()) {
                    startAutoReading();
                }
                
                // 如果啟用了定時點讚，啟動定時點讚系統
                if (globalState.config.liking.autoLiking.enabled) {
                    startAutoLikingSystem();
                }

                // 如果啟用了抽獎系統，啟動抽獎系統
                if (globalState.config.lottery.enabled) {
                    startLotterySystem();
                }

                // 啟動配置更新監聽器
                startConfigWatcher();
            }

            debugLog('腳本初始化完成');
        } catch (error) {
            debugLog('初始化失敗:', error);
        }
    }

    // === 抽獎自動回覆功能 ===

    // 獲取抽獎話題列表
    async function fetchLotteryTopics() {
        try {
            debugLog('開始獲取抽獎話題列表');
            const response = await fetch('https://linux.do/tag/%E6%8A%BD%E5%A5%96.json');

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            debugLog('抽獎話題API響應:', data);

            if (!data.topic_list || !data.topic_list.topics) {
                debugLog('API響應格式異常，未找到topics數組');
                return [];
            }

            return data.topic_list.topics;
        } catch (error) {
            debugLog('獲取抽獎話題失敗:', error);
            return [];
        }
    }

    // 檢查話題是否為今天創建
    function isTopicFromToday(createdAt) {
        const today = new Date();
        const topicDate = new Date(createdAt);

        return today.getFullYear() === topicDate.getFullYear() &&
               today.getMonth() === topicDate.getMonth() &&
               today.getDate() === topicDate.getDate();
    }

    // 獲取隨機回覆內容（避免重複）
    function getRandomReply() {
        const replies = globalState.config.lottery.replies;
        let availableReplies = replies.filter(reply => reply !== globalState.lottery.lastUsedReply);

        // 如果所有回覆都用過了，重置可用列表
        if (availableReplies.length === 0) {
            availableReplies = [...replies];
        }

        const randomReply = availableReplies[Math.floor(Math.random() * availableReplies.length)];
        globalState.lottery.lastUsedReply = randomReply;

        debugLog('選擇回覆內容:', randomReply);
        return randomReply;
    }

    // 檢查是否可以繼續回覆（小時限制）
    function canReplyMore() {
        const now = Date.now();
        const hoursPassed = (now - globalState.lottery.lastResetTime) / (1000 * 60 * 60);

        // 如果超過1小時，重置計數
        if (hoursPassed >= 1) {
            globalState.lottery.hourlyCount = 0;
            globalState.lottery.lastResetTime = now;
            debugLog('抽獎回覆計數已重置');
        }

        return globalState.lottery.hourlyCount < globalState.config.lottery.maxRepliesPerHour;
    }

    // 記錄回覆
    function recordReply() {
        globalState.lottery.hourlyCount++;
        globalState.lottery.todayProcessed++;
        debugLog(`記錄抽獎回覆，當前小時: ${globalState.lottery.hourlyCount}/${globalState.config.lottery.maxRepliesPerHour}`);
    }

    // 導航到話題頁面
    async function navigateToTopic(topicId) {
        try {
            debugLog('導航到話題:', topicId);
            const topicUrl = `https://linux.do/t/topic/${topicId}`;

            // 檢查是否已經在目標頁面
            if (window.location.href.includes(`/t/topic/${topicId}`) ||
                window.location.href.includes(`/${topicId}`)) {
                debugLog('已在目標話題頁面');
                return true;
            }

            window.location.href = topicUrl;

            // 等待頁面載入
            return new Promise((resolve) => {
                const checkLoaded = () => {
                    if (window.location.href.includes(topicId.toString())) {
                        debugLog('話題頁面載入完成');
                        resolve(true);
                    } else {
                        setTimeout(checkLoaded, 500);
                    }
                };
                setTimeout(checkLoaded, 1000);
            });
        } catch (error) {
            debugLog('導航到話題失敗:', error);
            return false;
        }
    }

    // 在話題頁面進行回覆
    async function replyToTopic(replyContent) {
        try {
            debugLog('開始回覆話題，內容:', replyContent);

            // 等待頁面完全載入
            await sleep(2000);

            // 查找回覆按鈕
            const replyButton = safeQuerySelector('.btn.btn-icon-text.post-action-menu__reply.reply.create');
            if (!replyButton) {
                debugLog('未找到回覆按鈕');
                return false;
            }

            debugLog('找到回覆按鈕，點擊');
            replyButton.click();

            // 等待編輯器出現
            await sleep(1500);

            // 查找文本輸入框
            const textArea = safeQuerySelector('.d-editor-input');
            if (!textArea) {
                debugLog('未找到文本輸入框');
                return false;
            }

            debugLog('找到文本輸入框，輸入內容');
            textArea.focus();
            textArea.value = replyContent;

            // 觸發輸入事件
            textArea.dispatchEvent(new Event('input', { bubbles: true }));

            // 等待一下再提交
            await sleep(1000);

            // 查找提交按鈕
            const submitButton = safeQuerySelector('.btn.btn-icon-text.btn-primary.create');
            if (!submitButton) {
                debugLog('未找到提交按鈕');
                return false;
            }

            debugLog('找到提交按鈕，提交回覆');
            submitButton.click();

            // 等待提交完成
            await sleep(2000);

            debugLog('回覆提交完成');
            return true;

        } catch (error) {
            debugLog('回覆話題失敗:', error);
            return false;
        }
    }

    // 處理單個抽獎話題
    async function processLotteryTopic(topic) {
        try {
            debugLog('開始處理抽獎話題:', topic.id, topic.title);

            // 檢查是否已處理過
            if (globalState.lottery.processedTopics.has(topic.id)) {
                debugLog('話題已處理過，跳過:', topic.id);
                return false;
            }

            // 檢查是否為今天的話題
            if (globalState.config.lottery.onlyToday && !isTopicFromToday(topic.created_at)) {
                debugLog('話題不是今天創建的，跳過:', topic.id);
                return false;
            }

            // 檢查回覆限制
            if (!canReplyMore()) {
                debugLog('已達到小時回覆限制，暫停處理');
                return false;
            }

            // 導航到話題頁面
            const navigated = await navigateToTopic(topic.id);
            if (!navigated) {
                debugLog('導航到話題失敗:', topic.id);
                return false;
            }

            // 獲取隨機回覆內容
            const replyContent = getRandomReply();

            // 隨機延遲
            const delay = getRandomDelay(
                globalState.config.lottery.replyDelay.min,
                globalState.config.lottery.replyDelay.max
            );
            debugLog(`等待 ${delay}ms 後回覆`);
            await sleep(delay);

            // 進行回覆
            const replied = await replyToTopic(replyContent);
            if (replied) {
                // 記錄成功處理
                globalState.lottery.processedTopics.add(topic.id);
                recordReply();
                debugLog('成功回覆抽獎話題:', topic.id, replyContent);
                return true;
            } else {
                debugLog('回覆抽獎話題失敗:', topic.id);
                return false;
            }

        } catch (error) {
            debugLog('處理抽獎話題出錯:', error);
            return false;
        }
    }


    // 抽獎系統主循環
    async function runLotterySystem() {
        try {
            if (!globalState.config.lottery.enabled) {
                debugLog('抽獎系統未啟用');
                return;
            }

            debugLog('開始執行抽獎系統循環');

            // 獲取抽獎話題列表
            const topics = await fetchLotteryTopics();
            if (topics.length === 0) {
                debugLog('未找到抽獎話題');
                return;
            }

            debugLog(`找到 ${topics.length} 個抽獎話題`);

            // 處理每個話題
            for (const topic of topics) {
                // 檢查是否還能繼續回覆
                if (!canReplyMore()) {
                    debugLog('已達到小時回覆限制，停止處理');
                    break;
                }

                // 處理話題
                const processed = await processLotteryTopic(topic);
                if (processed) {
                    // 成功處理後等待一段時間再處理下一個
                    const nextDelay = getRandomDelay(5000, 15000);
                    debugLog(`成功處理話題，等待 ${nextDelay}ms 處理下一個`);
                    await sleep(nextDelay);
                }
            }

            debugLog('抽獎系統循環完成');

        } catch (error) {
            debugLog('抽獎系統執行出錯:', error);
        }
    }

    // 啟動抽獎系統
    function startLotterySystem() {
        if (globalState.lottery.isActive) {
            debugLog('抽獎系統已在運行');
            return;
        }

        if (!globalState.config.lottery.enabled) {
            debugLog('抽獎系統未啟用');
            return;
        }

        debugLog('啟動抽獎自動回覆系統');
        globalState.lottery.isActive = true;

        // 立即執行一次
        runLotterySystem();

        // 設置定時器
        globalState.lottery.timer = setInterval(() => {
            if (globalState.config.lottery.enabled) {
                runLotterySystem();
            }
        }, globalState.config.lottery.checkInterval);

        debugLog(`抽獎系統已啟動，檢查間隔: ${globalState.config.lottery.checkInterval}ms`);
    }

    // 停止抽獎系統
    function stopLotterySystem() {
        if (!globalState.lottery.isActive) {
            debugLog('抽獎系統未在運行');
            return;
        }

        debugLog('停止抽獎自動回覆系統');
        globalState.lottery.isActive = false;

        if (globalState.lottery.timer) {
            clearInterval(globalState.lottery.timer);
            globalState.lottery.timer = null;
        }

        debugLog('抽獎系統已停止');
    }

    // 獲取抽獎系統狀態
    function getLotteryStatus() {
        const now = Date.now();
        const timeSinceReset = now - globalState.lottery.lastResetTime;
        const remainingTime = (60 * 60 * 1000) - timeSinceReset;
        const remainingReplies = globalState.config.lottery.maxRepliesPerHour - globalState.lottery.hourlyCount;

        return {
            isActive: globalState.lottery.isActive,
            enabled: globalState.config.lottery.enabled,
            hourlyCount: globalState.lottery.hourlyCount,
            maxHourly: globalState.config.lottery.maxRepliesPerHour,
            remaining: remainingReplies,
            resetIn: Math.max(0, remainingTime),
            todayProcessed: globalState.lottery.todayProcessed,
            processedTopics: globalState.lottery.processedTopics.size
        };
    }

    // === 初始化 ===
    function initialize() {
        debugLog('開始初始化一體化腳本');
        
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', onPageLoad);
        } else {
            setTimeout(onPageLoad, 500);
        }

        // 監聽頁面卸載，保存狀態
        window.addEventListener('beforeunload', () => {
            if (globalState.isRunning) {
                debugLog('頁面即將卸載，保存狀態');
                saveState(); // 保存運行狀態
                saveConfig(); // 保存配置
            }
            // 停止自動閱讀
            stopAutoReading();
            // 停止定時點讚系統
            stopAutoLikingSystem();
            // 停止抽獎系統
            stopLotterySystem();
            // 停止配置更新監聽器
            stopConfigWatcher();
        });
        
        // 監聽URL變化，自動啟動閱讀模擬
        let lastUrl = location.href;
        new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                debugLog('URL已變化:', url);
                
                // 停止之前的自動閱讀
                stopAutoReading();
                
                // 稍後啟動新的自動閱讀
                setTimeout(() => {
                    if (isTopicPage() && !globalState.isRunning) {
                        startAutoReading();
                    }
                }, 1000);
            }
        }).observe(document, {subtree: true, childList: true});
    }

    // 啟動腳本
    initialize();

})();