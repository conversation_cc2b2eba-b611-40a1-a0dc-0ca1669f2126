我現在想加一個功能對"https://linux.do/tag/%E6%8A%BD%E5%A5%96.json" 帶有"抽獎"這個標籤的話題進行自動回覆

比如

"参与一下"
"来抽奖啦"
"抽抽抽抽"
"中奖中奖"
"中中中中"
"参与参与"
"希望中奖"
"重在参与"
"支持支持"
"分母+1"
"来了来了"
"谢谢佬友"
"好运连连"
"春风得意"
"福星高照"
"吉星高照"
"鸿运齐天"

不要同時出現一樣的, 比如"参与一下" -> "参与一下" 不行, "参与一下" -> "来抽奖啦" -> "参与一下" 可以

就隨機吧

<button class="btn btn-icon-text post-action-menu__reply reply create fade-out btn-flat" title="开始撰写对此帖子的回复" aria-label="回复 @IFZZH 发布的帖子 #1" type="button">
<svg class="fa d-icon d-icon-reply svg-icon svg-string" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"><use href="#reply"></use></svg>      <span class="d-button-label">回复<!----></span>
    </button>
	
	應該是這個button
	
	<textarea aria-label="在此处输入。使用 Markdown、BBCode 或 HTML 进行格式化。拖动或粘贴图片。

在提问之前请善用论坛搜索功能关注是否有类似问题。编辑器右侧也会提示相似问题。

提问、回复请记得：真诚、友善、团结、专业，共建你我引以为荣之社区。" autocomplete="off" placeholder="在此处输入。使用 Markdown、BBCode 或 HTML 进行格式化。拖动或粘贴图片。

在提问之前请善用论坛搜索功能关注是否有类似问题。编辑器右侧也会提示相似问题。

提问、回复请记得：真诚、友善、团结、专业，共建你我引以为荣之社区。" id="ember630" class="ember-text-area ember-view d-editor-input" data-handled="true" style=""></textarea>

在這裡輸入

輸入我上方""的內容


<button class="btn btn-icon-text btn-primary create" title="或按 Ctrl+Enter" type="button">
<svg class="fa d-icon d-icon-reply svg-icon svg-string" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"><use href="#reply"></use></svg>      <span class="d-button-label">回复<!----></span>
    </button>
	
再點擊這個

再到下一個話題


    ],
    "topics": [
      {
        "id": 833745,
        "title": "三级啦，送个CodeBuddy IDE邀请码",
        "fancy_title": "三级啦，送个CodeBuddy IDE邀请码",
        "slug": "topic",
        "posts_count": 73,
        "reply_count": 0,
        "highest_post_number": 73,
        "image_url": null,
        "created_at": "2025-08-01T00:27:49.779Z",
        "last_posted_at": "2025-08-01T03:04:40.577Z",
        "bumped": true,
        "bumped_at": "2025-08-01T03:04:40.577Z",
        "archetype": "regular",
        "unseen": true,
        "pinned": false,
        "unpinned": null,
        "visible": true,
        "closed": false,
        "archived": false,
        "bookmarked": null,
        "liked": null,
        "thumbnails": null,
        "tags": [
          "抽奖"
        ],
        "tags_descriptions": {

        },
        "views": 216,
        "like_count": 38,
        "has_summary": true,
        "last_poster_username": "idear",
        "category_id": 36,
        "pinned_globally": false,
        "featured_link": null,
        "has_accepted_answer": false,
        "can_have_answer": false,
        "can_vote": false,
        "posters": [
          {
            "extras": null,
            "description": "原始发帖人",
            "user_id": 143135,
            "primary_group_id": null,
            "flair_group_id": null
          },
          {
            "extras": null,
            "description": "频繁发帖人",
            "user_id": 24907,
            "primary_group_id": null,
            "flair_group_id": null
          },
          {
            "extras": null,
            "description": "频繁发帖人",
            "user_id": 10450,
            "primary_group_id": null,
            "flair_group_id": null
          },
          {
            "extras": null,
            "description": "频繁发帖人",
            "user_id": 6182,
            "primary_group_id": null,
            "flair_group_id": 13
          },
          {
            "extras": "latest",
            "description": "最新发帖人",
            "user_id": 2944,
            "primary_group_id": 83,
            "flair_group_id": 13
          }
        ]
      },
      {
        "id": 833932,
        "title": "[抽奖] 抽一个B站大会员",
        "fancy_title": "[抽奖] 抽一个B站大会员",
        "slug": "topic",
        "posts_count": 172,
        "reply_count": 1,
        "highest_post_number": 172,
        "image_url": null,
        "created_at": "2025-08-01T01:37:55.130Z",
        "last_posted_at": "2025-08-01T03:04:34.265Z",
        "bumped": true,
        "bumped_at": "2025-08-01T03:04:34.265Z",
        "archetype": "regular",
        "unseen": false,
        "last_read_post_number": 124,
        "unread": 0,
        "new_posts": 48,
        "unread_posts": 48,
        "pinned": false,
        "unpinned": null,
        "visible": true,
        "closed": false,
        "archived": false,
        "notification_level": 2,
        "bookmarked": false,
		
		
		大概長這樣
		
		
		我只要當中的, 比如今日是8/1號, 就只回覆8/1號的